# Python build
.eggs/
gradio.egg-info
dist/
*.pyc
__pycache__/
*.py[cod]
*$py.class
build/
__tmp/*
*.pyi
py.typed

# JS build
gradio/templates/*
gradio/node/*
gradio/_frontend_code/*
js/gradio-preview/test/*

# Secrets
.env

# Gradio run artifacts
*.db
*.sqlite3
gradio/launches.json
flagged/
gradio_cached_examples/
tmp.zip

# Tests
.coverage
coverage.xml
test.txt
**/snapshots/**/*.png
playwright-report/

# Demos
demo/tmp.zip
demo/files/*.avi
demo/files/*.mp4
demo/all_demos/demos/*
demo/all_demos/requirements.txt
demo/*/config.json
demo/annotatedimage_component/*.png

# Etc
.idea/*
.DS_Store
*.bak
workspace.code-workspace
*.h5

# dev containers
.pnpm-store/

# log files
.pnpm-debug.log

# Local virtualenv for devs
.venv*

# FRP
gradio/frpc_*
.vercel

# js
node_modules
public/build/
test-results
client/js/test.js
.config/test.py

# storybook
storybook-static
build-storybook.log
js/storybook/theme.css

# playwright
.config/playwright/.cache

# chrome
app/chroma_db

/uploads
