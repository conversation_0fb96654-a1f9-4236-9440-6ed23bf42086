from typing import List, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import StreamingResponse

from .chat_sessions import get_chat_session

from ..dependencies import authenticate_user
from supabase import create_client, Client
import os
from pydantic import BaseModel
from utils.chat.chat_hkbu_chatgpt import chat_by_hkbu_chatgpt_api, parse_hkbu_chatgpt_api_res
from utils.chat.chat_openrouter import chat_by_openrouter_api, parse_openrouter_api_res, chat_by_openrouter_api_streaming
from utils.chat.chat_azure_openai import chat_by_azure_openai_api, parse_azure_openai_api_res
from utils.chat.chat_qwen import chat_by_qwen_api, parse_qwen_api_res
from utils.image.image_azure_dalle import image_by_azure_dalle, parse_azure_dalle_api_res
from dotenv import load_dotenv
import asyncio
from collections import defaultdict

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

# Global dictionary to store active streaming sessions
# Structure: {user_id: {session_id: cancel_event}}
active_streams = defaultdict(dict)

router = APIRouter(
    prefix="/api",
    tags=["messages"],
)

# Model Dictionary for Chat by HKBU ChatGPT API
model_dict_chat_by_hkbu_chatgpt_api = {
    "HKBU ChatGPT (gpt-35-turbo)": "gpt-35-turbo",
    "HKBU ChatGPT (gpt-4-turbo)": "gpt-4-turbo"
}
# Model Dictionary for Chat by OpenRouter API
model_dict_chat_by_openrouter_api = {
    "OpenAI (o1-preview)": "openai/o3-mini-high",  # Compatible with OpenAI o1-preview
    "OpenAI (o3-mini-high)": "openai/o3-mini-high",
    "OpenAI (o1-mini)": "openai/o1-mini",  # Compatible with OpenAI o1-mini (openai/o1-mini-2024-09-12)
    "OpenAI (gpt-4.1)": "openai/gpt-4.1",
    "OpenAI (gpt-4.1-mini)": "openai/gpt-4.1-mini",
    "OpenAI (gpt-4o)": "openai/gpt-4o-2024-11-20",  # Compatible with OpenAI GPT-4o
    "OpenAI (gpt-3.5-turbo-0125)": "openai/gpt-4o-mini",  # Compatible with OpenAI GPT-3.5 Turbo
    "OpenAI (gpt-4o-mini)": "openai/gpt-4.1-mini",  # Compatible with OpenAI GPT-4o-mini
    "OpenAI (gpt-4-turbo-preview)": "openai/gpt-4.1",  # Compatible with OpenAI GPT-4 Turbo Preview
    "OpenAI (gpt-4-turbo)": "openai/gpt-4.1",  # Compatible with OpenAI GPT-4 Turbo
    "Anthropic Claude 3.5 (claude-3.5-sonnet)": "anthropic/claude-3.5-sonnet",
    "Anthropic Claude 3.5 (claude-3.5-haiku)": "anthropic/claude-3-5-haiku",
    # Compatible with Anthropic Claude 3 (claude-3-haiku:beta)
    "Anthropic Claude 3 (claude-3-haiku:beta)": "anthropic/claude-3-haiku",
    "Anthropic Claude 3 (claude-3-haiku)": "anthropic/claude-3-haiku",
    # Compatible with Anthropic Claude 3 (claude-3-sonnet:beta)
    "Anthropic Claude 3 (claude-3-sonnet:beta)": "anthropic/claude-3-sonnet",
    "Anthropic Claude 3 (claude-3-sonnet)": "anthropic/claude-3-sonnet",
    # Compatible with Anthropic Claude 3 (claude-3-opus:beta)
    "Anthropic Claude 3 (claude-3-opus:beta)": "anthropic/claude-3-opus",
    "Anthropic Claude 3 (claude-3-opus)": "anthropic/claude-3-opus",   
    "DeepSeek R1 (deepseek-r1)": "deepseek/deepseek-r1",
    "DeepSeek V3 (deepseek-chat)": "deepseek/deepseek-chat-v3-0324", # Compatible with DeepSeek V3
    "Qwen QvQ (qvq-72b-preview)": "qwen/qwen-vl-max", # Compatible with Qwen QvQ (qvq-72b-preview)
    "Qwen2.5 (qwen-2.5-coder-32b-instruct)": "qwen/qwen-2.5-coder-32b-instruct",
    "Qwen2.5 (qwen-2.5-72b-instruct)": "qwen/qwen-2.5-72b-instruct",
    "Mistral AI (mixtral-8x7b-instruct)": "mistralai/mixtral-8x7b-instruct",
    "MythoMax (mythomax-l2-13b)": "gryphe/mythomax-l2-13b",
    "MiniMax-01 (minimax-01)": "minimax/minimax-01",
    "xAI Grok 2 (grok-2-1212)": "x-ai/grok-2-1212",
    "xAI Grok Beta (grok-beta)": "x-ai/grok-beta",
    "Google Gemini (gemini-pro-1.5)": "google/gemini-pro-1.5",
    "Google Gemini (gemini-pro)": "google/gemini-pro",
    "Google Gemma (google/gemma-7b-it)": "google/gemma-2-27b-it",  # Compatible with Google Gemma 7b IT
    "Google Gemma (google/gemma-2-27b-it)": "google/gemma-2-27b-it",
    "Meta Llama 3.3 (llama-3.3-70b-instruct)": "meta-llama/llama-3.3-70b-instruct",
    "Meta Llama 3.2 (llama-3.2-3b-instruct)": "meta-llama/llama-3.2-3b-instruct",
    "Meta Llama 3.2 (llama-3.2-90b-vision-instruct)": "meta-llama/llama-3.2-90b-vision-instruct",
    "Meta Llama 3.1 (llama-3.1-8b-instruct)": "meta-llama/llama-3.1-8b-instruct",
    "Meta Llama 3.1 (llama-3.1-70b-instruct)": "meta-llama/llama-3.1-70b-instruct",
    "Meta Llama 3.1 (llama-3.1-405b-instruct)": "meta-llama/llama-3.1-405b-instruct",
    "Meta Llama 3 (llama-3-8b-instruct)": "meta-llama/llama-3-8b-instruct",
    "Meta Llama 3 (llama-3-70b-instruct)": "meta-llama/llama-3-70b-instruct",
    "Meta Llama 2 (llama-2-13b-chat)": "meta-llama/llama-2-13b-chat",
    "Meta Llama 2 (llama-2-70b-chat)": "meta-llama/llama-2-70b-chat",
    "Perplexity Llama 3.1 Online (llama-3.1-sonar-huge-128k-online)": "perplexity/llama-3.1-sonar-large-128k-online",  # Compatible with "huge"
    "Perplexity Llama 3.1 Online (llama-3.1-sonar-large-128k-online)": "perplexity/llama-3.1-sonar-large-128k-online"  # Compatible with "large"
}
# Model Dictionary for Chat by Azure OpenAI API
model_dict_chat_by_azure_openai_api = {
    "Azure OpenAI (gpt-35-turbo-16k)": "gpt-35-turbo-16k"
}
# Model Dictionary for Chat by Qwen API
model_dict_chat_by_qwen_api = {
    "Tongyi Qwen (qwen-turbo)": "qwen-turbo",
    "Tongyi Qwen (qwen-plus)": "qwen-plus"
}
# Model Dictionary for Image by Azure Dalle API
model_dict_image_by_azure_dalle_api = {
    "Azure Dall-E 3 (dall-e-3-1024)": "dall-e-3"
}


class Chatbot(BaseModel):
    chatbot_id: str
    id: int
    created_at: str
    chatbot_name: str
    model_name: str
    system_prompt: str
    temperature: float
    type_name: str
    description: Dict[str, Any] | None
    knowledge_base_persist_directory: str | None
    knowledge_base_file_paths: List[str] | None
    knowledge_base_file_names: List[str] | None
    knowledge_base_embedding_model: str | None
    updated_at: str | None
    deleted_at: str | None


# Define the request body model for chat messages
class ChatMessageItem(BaseModel):
    session_id: str
    conversation_list: List[Any]
    chatbot: Chatbot


async def store_chat_response_in_db(chatbot, session_id, chatbot_id, res, time_elapsed):
    # Prepare the record for database insertion
    db_record = None
    if chatbot.model_name in model_dict_chat_by_hkbu_chatgpt_api:
        db_record = parse_hkbu_chatgpt_api_res(session_id, chatbot_id, res, time_elapsed)
    elif chatbot.model_name in model_dict_chat_by_openrouter_api:
        db_record = parse_openrouter_api_res(session_id, chatbot_id, res, time_elapsed)
    elif chatbot.model_name in model_dict_chat_by_azure_openai_api:
        db_record = parse_azure_openai_api_res(session_id, chatbot_id, res, time_elapsed)
    elif chatbot.model_name in model_dict_chat_by_qwen_api:
        db_record = parse_qwen_api_res(session_id, chatbot_id, res, time_elapsed)

    # Insert the record into the database
    if db_record:
        try:
            response = supabase.table("chatbot_responses_v2").insert([db_record]).execute()
            if hasattr(response, 'data') and response.data:
                return response.data[0]
            return db_record  # Return the original record if response.data is not available
        except Exception as e:
            print(f"Error storing chat response: {e}")
            return db_record  # Return the original record in case of error

    return None


async def store_image_response_in_db(chatbot, session_id, chatbot_id, res, time_elapsed):
    # Prepare the record for database insertion
    db_record = None
    if chatbot.model_name in model_dict_image_by_azure_dalle_api:
        db_record = parse_azure_dalle_api_res(session_id, chatbot_id, res, time_elapsed)

    # Insert the record into the database
    if db_record:
        try:
            response = supabase.table("tti_responses_v2").insert([db_record]).execute()
            if hasattr(response, 'data') and response.data:
                return response.data[0]
            return db_record  # Return the original record if response.data is not available
        except Exception as e:
            print(f"Error storing image response: {e}")
            return db_record  # Return the original record in case of error

    return None


# Handle incoming (new) messages from the chatbot
@router.post("/messages")
async def handle_new_messages(messages_data: ChatMessageItem, user_id: str = Depends(authenticate_user)):
    session_id = messages_data.session_id
    chatbot = messages_data.chatbot
    get_chat_session_response = await get_chat_session(session_id, user_id)
    if len(get_chat_session_response) == 0:
        return {
            "chat_response": "Session not found, please check your login status.",
            "res": "Session not found",
            "time_elapsed": 0
        }

    conversation_list = messages_data.conversation_list
    chat_response = ""
    res = ""
    time_elapsed = 0
    # Check if the persist_directory is None or empty, if not None or empty, load the knowledge base
    # Then perform the conversation with the knowledge base
    if chatbot.model_name in model_dict_chat_by_hkbu_chatgpt_api:
        chat_response, res, time_elapsed = await chat_by_hkbu_chatgpt_api(
            conversation_list=conversation_list,
            model_name=model_dict_chat_by_hkbu_chatgpt_api[chatbot.model_name],
            temperature=chatbot.temperature
        )
    elif chatbot.model_name in model_dict_chat_by_openrouter_api:
        chat_response, res, time_elapsed = await chat_by_openrouter_api(
            conversation_list=conversation_list,
            model_name=model_dict_chat_by_openrouter_api[chatbot.model_name],
            temperature=chatbot.temperature
        )
    elif chatbot.model_name in model_dict_chat_by_azure_openai_api:
        chat_response, res, time_elapsed = await chat_by_azure_openai_api(
            conversation_list=conversation_list,
            model_name=model_dict_chat_by_azure_openai_api[chatbot.model_name],
            temperature=chatbot.temperature
        )
    elif chatbot.model_name in model_dict_chat_by_qwen_api:
        chat_response, res, time_elapsed = await chat_by_qwen_api(
            conversation_list=conversation_list,
            model_name=model_dict_chat_by_qwen_api[chatbot.model_name],
            temperature=chatbot.temperature
        )
    elif chatbot.model_name in model_dict_image_by_azure_dalle_api:
        chat_response, res, time_elapsed = image_by_azure_dalle(
            conversation_list=conversation_list,
            model_name=model_dict_image_by_azure_dalle_api[chatbot.model_name]
        )

    if chatbot.model_name in model_dict_image_by_azure_dalle_api:
        store_res = await store_image_response_in_db(chatbot, session_id, chatbot.chatbot_id, res, time_elapsed)
    else:
        store_res = await store_chat_response_in_db(chatbot, session_id, chatbot.chatbot_id, res, time_elapsed)

    return {
        "chat_response": chat_response,
        "res": res,
        "store_res": store_res,
        "time_elapsed": time_elapsed
    }


# Handle incoming (new) messages from the chatbot with streaming response
@router.post("/messages-stream")
async def handle_streaming_messages(messages_data: ChatMessageItem, request: Request, user_id: str = Depends(authenticate_user)):
    session_id = messages_data.session_id
    chatbot = messages_data.chatbot
    get_chat_session_response = await get_chat_session(session_id, user_id)
    if len(get_chat_session_response) == 0:
        return {
            "chat_response": "Session not found, please check your login status.",
            "res": "Session not found",
            "time_elapsed": 0
        }

    conversation_list = messages_data.conversation_list
    
    # Create a cancellation event
    cancel_event = asyncio.Event()
    
    # Store the cancel event in the global dictionary
    active_streams[user_id][session_id] = cancel_event
    
    # Currently only supporting OpenRouter API for streaming
    if chatbot.model_name in model_dict_chat_by_openrouter_api:
        async def stream_generator():
            try:
                async for chunk, res, time_elapsed in chat_by_openrouter_api_streaming(
                    conversation_list=conversation_list,
                    model_name=model_dict_chat_by_openrouter_api[chatbot.model_name],
                    temperature=chatbot.temperature
                ):
                    # Check for cancellation before processing each chunk
                    if cancel_event.is_set():
                        yield f"data: [CANCELLED]\n\n"
                        return

                    if isinstance(chunk, dict):
                        if chunk["type"] == "cancelled":
                            # Handle cancellation
                            yield f"data: [CANCELLED]\n\n"
                            return
                        elif chunk["type"] == "full_response":
                            # Handle final response
                            if res:
                                await store_chat_response_in_db(chatbot, session_id, chatbot.chatbot_id, res, time_elapsed)
                            yield "data: [DONE]\n\n"
                            return
                    
                    if isinstance(chunk, str):
                        if chunk.startswith("Error") or chunk.startswith("Connection Error") or chunk.startswith("System Error"):
                            yield f"data: {chunk}\n\n"
                            return
                        
                        # Regular content chunk
                        sse_data = f"data: {chunk}\n\n"
                        yield sse_data
                        
            except asyncio.CancelledError:
                yield f"data: [CANCELLED]\n\n"
                return
            finally:
                # Clean up the cancel event when the stream ends
                if session_id in active_streams[user_id]:
                    del active_streams[user_id][session_id]
                if not active_streams[user_id]:  # If user has no more active sessions
                    del active_streams[user_id]
            
        return StreamingResponse(stream_generator(), media_type="text/event-stream")
    else:
        return {
            "chat_response": "Streaming is only supported for OpenRouter API models.",
            "res": "Streaming not supported",
            "time_elapsed": 0
        }

# Add a new endpoint to handle cancellation
@router.post("/messages-stream/cancel/{session_id}")
async def cancel_streaming(session_id: str, user_id: str = Depends(authenticate_user)):
    # Get the cancel event from the global dictionary
    cancel_event = active_streams.get(user_id, {}).get(session_id)
    
    if cancel_event:
        # Set the cancel event
        cancel_event.set()
        return {"status": "cancelled", "session_id": session_id}
    
    return {"status": "no active stream found", "session_id": session_id}

# Add an endpoint to get active streaming sessions for a user
@router.get("/messages-stream/active")
async def get_active_streams(user_id: str = Depends(authenticate_user)):
    user_sessions = list(active_streams.get(user_id, {}).keys())
    return {"active_sessions": user_sessions}
