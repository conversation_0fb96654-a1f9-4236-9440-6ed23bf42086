from fastapi import APIRouter, Depends, HTTPException
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["notifications"],
)


@router.get("/notification-list")
async def get_notification_list(
    user_id: str = Depends(authenticate_user),
    notification_type: str = None
):
    # Getting notifications for the user
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    notification_data = []
    # Query notification information according to user roles
    if user_role == "Student":
        # Student Role: Query notifications for a student
        response = supabase.from_("notification_item").select("*").eq("student_id", user_id).execute()
        # Filter the deleted_at field
        notification_data = [notification for notification in response.data if not notification["deleted_at"]]
        
        # Filter by notification type if specified and add course_title for display
        if notification_type:
            filtered_data = []
            for notification in notification_data:
                description = notification.get("description", {})
                if isinstance(description, dict) and description.get("type") == notification_type:
                    # Add course_title to the top level for frontend compatibility
                    notification["course_title"] = description.get("course_title", "Unknown Course")
                    filtered_data.append(notification)
            notification_data = filtered_data
        else:
            # Add course_title for all notifications
            for notification in notification_data:
                description = notification.get("description", {})
                if isinstance(description, dict):
                    notification["course_title"] = description.get("course_title", "Unknown Course")
                    
    elif user_role == "Teacher":
        # Teacher Role: Query notifications for a teacher with course information
        response = supabase.from_("notifications").select("*, courses(course_title)").eq("creator_user_id", user_id).execute()
        # Filter the deleted_at field
        notification_data = [notification for notification in response.data if not notification["deleted_at"]]
        
        # Add course_title to top level and filter by notification type if specified
        if notification_type:
            filtered_data = []
            for notification in notification_data:
                # Add course_title to the notification object
                if notification.get("courses") and notification["courses"].get("course_title"):
                    notification["course_title"] = notification["courses"]["course_title"]
                else:
                    # Fallback to description.course_title if available
                    description = notification.get("description", {})
                    notification["course_title"] = description.get("course_title", "Unknown Course")
                
                # Remove the courses object as it's no longer needed
                notification.pop("courses", None)
                
                # Filter by type
                description = notification.get("description", {})
                if isinstance(description, dict) and description.get("type") == notification_type:
                    filtered_data.append(notification)
            notification_data = filtered_data
        else:
            # Add course_title for all notifications
            for notification in notification_data:
                if notification.get("courses") and notification["courses"].get("course_title"):
                    notification["course_title"] = notification["courses"]["course_title"]
                else:
                    description = notification.get("description", {})
                    notification["course_title"] = description.get("course_title", "Unknown Course")
                
                # Remove the courses object as it's no longer needed
                notification.pop("courses", None)
    else:
        # Other roles: return nothing
        return []

    return notification_data


class AddNotificationData(BaseModel):
    course_id: str
    notification_title: str
    notification_content: str


@router.post("/notification")
async def add_notification(data: AddNotificationData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    if user_role != "Teacher":
        raise HTTPException(status_code=403, detail="You are not authorized to create notifications")

    # Insert notification data
    response = supabase.from_("notifications").insert([{
        "course_id": data.course_id,
        "creator_user_id": user_id,
        "notification_title": data.notification_title,
        "description": {
            "content": data.notification_content
        },
    }]).execute()

    if not response.data:
        raise HTTPException(status_code=500, detail="Failed to create notification")

    return response.data[0]


@router.delete("/notification")
async def delete_notification(notification_id: str, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Try to find the notification in both tables
    notification_found = False
    response_data = None

    if user_role == "Teacher":
        # For teachers, delete from notifications table
        response = supabase.from_("notifications") \
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()}) \
            .eq("notification_id", notification_id) \
            .execute()

        if response.data:
            notification_found = True
            response_data = response.data[0]

    elif user_role == "Student":
        # For students, delete from notification_item table
        # First verify the notification belongs to this student
        notification_check = supabase.from_("notification_item") \
            .select("student_id") \
            .eq("notification_id", notification_id) \
            .execute()

        if notification_check.data and notification_check.data[0]["student_id"] == user_id:
            response = supabase.from_("notification_item") \
                .update({"deleted_at": datetime.datetime.utcnow().isoformat()}) \
                .eq("notification_id", notification_id) \
                .execute()

            if response.data:
                notification_found = True
                response_data = response.data[0]
        else:
            raise HTTPException(status_code=403, detail="Access denied to this notification")

    if not notification_found:
        raise HTTPException(status_code=404, detail="Notification not found")

    return response_data
