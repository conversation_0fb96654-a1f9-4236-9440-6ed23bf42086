from fastapi import APIRouter, Depends, HTTPException, Query
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
from dotenv import load_dotenv
import datetime

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["enrollments"],
)


class EmailEnrollmentData(BaseModel):
    group_id: str
    email: str


@router.post("/email-enrollment")
async def email_enrollment(data: EmailEnrollmentData, user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    response = None
    if user_role == "Student" or user_role == "Teacher":
        # Teacher Role: Enroll a student in a student group
        # Query student's user information by email
        student_response = supabase.from_("users").select("user_id").eq("email", data.email).execute()

        if not student_response.data:
            raise HTTPException(status_code=404, detail="Student not found")

        student_id = student_response.data[0]["user_id"]

        # Check if student is already enrolled in the group
        enrollments_response = supabase.from_("enrollments")\
            .select("*")\
            .eq("group_id", data.group_id)\
            .eq("student_id", student_id)\
            .execute()

        # Filter out deleted enrollments
        enrollment_list = [enrollment for enrollment in enrollments_response.data if not enrollment["deleted_at"]]
        if enrollment_list:
            raise HTTPException(status_code=404, detail="Student already enrolled in the group")

        # Enroll student in the group
        response = supabase.from_("enrollments")\
            .insert({"group_id": data.group_id, "student_id": student_id})\
            .execute()
    if not response.data:
        raise HTTPException(status_code=404, detail="Enrollment failed")

    return response.data


@router.delete("/enrollment")
async def delete_enrollment(group_id: str, student_id: str, user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    response = None
    if user_role == "Student":
        # Student Role: To be implemented
        return []
    elif user_role == "Teacher":
        # Teacher Role: Remove a student from a student group
        response = supabase.from_("enrollments")\
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()})\
            .eq("group_id", group_id)\
            .eq("student_id", student_id)\
            .execute()
    if not response.data:
        raise HTTPException(status_code=404, detail="Enrollment deletion failed")

    return response.data
