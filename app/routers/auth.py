from fastapi import APIRouter, Depends, HTTPException, Body
from ..dependencies import authenticate_user
from pydantic import BaseModel, EmailStr, constr
import bcrypt
import jwt
import datetime
from ..models import User
from ..dependencies import jwt_secret_key
from supabase import create_client, Client
import resend
import os
import uuid
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

# Configure the Resend API key
resend_api_key: str = os.environ.get("RESEND_API_KEY")
resend.api_key = resend_api_key

router = APIRouter(
    prefix="/api",
    tags=["auth"],
)


class RegisterUser(BaseModel):
    fullName: str
    email: EmailStr
    password: constr(min_length=6)
    role: str
    personnelId: str


class LoginUser(BaseModel):
    email: EmailStr
    password: constr(min_length=6)


class ChangePassword(BaseModel):
    current_password: constr(min_length=6)
    new_password: constr(min_length=6)


@router.post("/register")
async def register_user(user: RegisterUser):
    response = supabase.from_("users").select("*").eq("email", user.email).execute()
    if response.data:
        raise HTTPException(status_code=400, detail="Email is already registered")

    hashed_password = bcrypt.hashpw(user.password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    # Generate a unique identifier
    unique_id = str(uuid.uuid4())[:5]

    new_user = {
        "user_id": str(uuid.uuid4()),  # Generate User UUID
        "username": "_".join(user.fullName.lower().split()) + "_" + unique_id,
        "full_name": user.fullName,
        "email": user.email,
        "password": hashed_password,
        "personnel_id": user.personnelId,
        "role_name": user.role,
        "activation_token": str(uuid.uuid4()),  # Generate Activation Token
        "description": {}  # Initialise to a null object
    }

    new_user_response, count = supabase.from_("users").insert(new_user).execute()

    if not new_user_response:
        raise HTTPException(status_code=500, detail="Failed to create user")

    # Send activation email
    send_activation_email(user.fullName, user.email, new_user["activation_token"])

    if user.role == "Student":
        # If the new user is a student, enroll the student into the Open Courses Student Group
        enrollment_response = supabase.from_("enrollments") \
            .insert({"group_id": "8e58a61c-d034-4cbb-a0c0-8bd527dc890b", "student_id": new_user["user_id"]}) \
            .execute()

        if not enrollment_response:
            raise HTTPException(status_code=500, detail="Failed to enroll student")
    elif user.role == "Teacher":
        # If the new user is a teacher
        # Add a record to the groups_courses table:
        # for the Open Courses Student Group and Open Courses with the new teacher_id
        # So that the new user can use all GenericChatbots

        group_course_response = supabase.from_("groups_courses").insert([{
            "group_id": "8e58a61c-d034-4cbb-a0c0-8bd527dc890b",
            "course_id": "0b99ca04-70c7-47cc-98c5-c39e6fbad057",
            "teacher_id": new_user["user_id"],
            "start_date": datetime.date.today().isoformat(),
            "end_date": (datetime.date.today() + datetime.timedelta(days=365)).isoformat(),
        }]).execute()

        if not group_course_response.data:
            raise HTTPException(status_code=500, detail="Failed to create default group course")

    return {"message": "User created successfully"}


BYTEWISE_BASE_URL = "https://chat.hkbu.life"


def send_activation_email(full_name: str, email: str, activation_token: str):
    # Construct the activation URL
    activation_url = f"{BYTEWISE_BASE_URL}/#/activate/{activation_token}"

    params: resend.Emails.SendParams = {
        "from": "Bytewise <<EMAIL>>",
        "to": [email],
        "subject": "Welcome to Bytewise! Let’s Get Your Account Set Up 🚀",
        "html": f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activate Your Bytewise Account</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f7f7f7; border-radius: 10px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);">
        <h2 style="color: #2c3e50;">Hi {full_name},</h2>

        <p>We’re thrilled to have you join <strong>Bytewise</strong>! Let's get you started and unlock the AI-assisted, personalized teaching and learning experience we’ve prepared for you.</p>

        <p>To activate your account and get everything set up, please click the link below:</p>

        <a href="{activation_url}" style="display: inline-block; background-color: #3498db; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px; margin-top: 20px;">Activate My Account</a>

        <p>If the button above doesn’t work, copy and paste the following link into your browser:</p>
        
        <p style="word-wrap: break-word;"><a href="{activation_url}" style="color: #3498db;">{activation_url}</a></p>

        <p>Thank you for choosing <strong>Bytewise</strong>! We can't wait to have you on board.</p>

        <p style="margin-top: 20px;">Best regards,</p>
        <p><strong>Bytewise Team</strong></p>
    </div>
</body>
</html>
"""
    }

    # Send email
    resend.Emails.send(params)

    return {"message": "Activation email sent successfully"}


@router.post("/login")
async def login_user(user: LoginUser):
    response = supabase.from_("users").select("*").eq("email", user.email).execute()
    if not response.data:
        raise HTTPException(status_code=400, detail="Invalid email or password")

    user_data = response.data[0]
    print(user_data)

    # Check if the user is activated
    if not user_data["is_active"]:
        raise HTTPException(status_code=403, detail="User is not activated, please check your email for the activation link")

    if not bcrypt.checkpw(user.password.encode('utf-8'), user_data["password"].encode('utf-8')):
        raise HTTPException(status_code=400, detail="Invalid email or password")

    payload = {
        "user_id": user_data["user_id"],
        "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=24)
    }
    token = jwt.encode(payload, jwt_secret_key, algorithm="HS256")

    return {"message": "User logged in successfully", "token": token, "userData": user_data}


class ActivateAccountData(BaseModel):
    token: str


@router.put("/activate")
async def activate_account(data: ActivateAccountData):
    response = supabase.from_("users").select("*").eq("activation_token", data.token).execute()
    if not response.data:
        raise HTTPException(status_code=404, detail="Invalid activation token")

    user_data = response.data[0]

    data, count = supabase.from_("users").update({"is_active": True, "activation_token": None}).eq("user_id",
                                                                                                  user_data["user_id"]).execute()
    if not data:
        raise HTTPException(status_code=500, detail="Failed to activate account")

    return {"message": "Account activated successfully, redirecting to login page in 5 seconds..."}


@router.put("/change-password")
async def change_password(password_data: ChangePassword, user_id: str = Depends(authenticate_user)):
    response = supabase.from_("users").select("*").eq("user_id", user_id).execute()
    if not response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_data = response.data[0]

    if not bcrypt.checkpw(password_data.current_password.encode('utf-8'), user_data["password"].encode('utf-8')):
        raise HTTPException(status_code=400, detail="Invalid current password")

    hashed_password = bcrypt.hashpw(password_data.new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    data, count = supabase.from_("users").update(
        {"password": hashed_password, "updated_at": datetime.datetime.utcnow().isoformat()}).eq("user_id",
                                                                                                user_id).execute()
    if not data:
        raise HTTPException(status_code=500, detail="Failed to update password")

    return {"message": "Password updated successfully"}
