from datetime import datetime
from typing import List, Any

from fastapi import APIRouter, Depends, HTTPException, Query, Form

from utils.chat.chat_openrouter import chat_by_openrouter_api
from utils.doc.doc_azure import analyze_document
from utils.prompts.prompt_web_search import get_prompt_web_search
from utils.prompts.prompt_get_checklist_progress import get_prompt_checklist_progress
from ..dependencies import authenticate_user
from supabase import create_client, Client
import os
from pydantic import BaseModel
from dotenv import load_dotenv

from fastapi import File, UploadFile
import aiofiles
from uuid import uuid4

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["chat_sessions"],
)


# Get chat session information by session ID and student ID
@router.get("/chat-session-by-student")
async def get_chat_session_by_student(session_id: str, student_id: str, user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    if user_role != "Teacher":
        raise HTTPException(status_code=403, detail="Permission denied")

    # Query the chat session information
    response = supabase.from_("chat_sessions_v2")\
        .select("*")\
        .eq("session_id", session_id)\
        .eq("user_id", student_id)\
        .execute()
    if not response.data:
        return []

    return response.data[0]


# Get chat session information by session ID
@router.get("/chat-session")
async def get_chat_session(session_id: str = Query(...), user_id: str = Depends(authenticate_user)):
    # Query the chat session information
    response = supabase.from_("chat_sessions_v2")\
        .select("*")\
        .eq("session_id", session_id)\
        .eq("user_id", user_id)\
        .execute()
    if not response.data:
        return []

    return response.data[0]


# Define the request body model for creating a new chat session
class AddChatSessionData(BaseModel):
    chatbot_id: str
    module_id: str


# Add a new chat session to the database
@router.post("/chat-session")
async def add_chat_session(session_data: AddChatSessionData, user_id: str = Depends(authenticate_user)):
    # Query existing chat sessions
    existing_sessions_response = supabase \
        .from_("chat_sessions_v2") \
        .select("*") \
        .eq("user_id", user_id) \
        .eq("chatbot_id", session_data.chatbot_id) \
        .eq("module_id", session_data.module_id) \
        .execute()

    max_session_index = 0
    for session in existing_sessions_response.data:
        # Skip deleted chat sessions
        deleted_at = session['deleted_at']
        if deleted_at:
            continue
        session_index = session["session_index"]
        if session_index > max_session_index:
            max_session_index = session_index

    new_session_index = max_session_index + 1

    # Generate a new session name according to the current_session_index
    new_session_name = f"Session {new_session_index}"

    # Insert a new chat session into the database
    response = supabase.from_("chat_sessions_v2").insert([{
        "chatbot_id": session_data.chatbot_id,
        "module_id": session_data.module_id,
        "user_id": user_id,
        "session_index": new_session_index,
        "session_name": new_session_name,
    }]).execute()

    if not response.data:
        return []

    new_session_data = response.data[0]

    return new_session_data


# Delete a chat session from the database
@router.delete("/chat-session")
async def delete_chat_session(session_id: str, user_id: str = Depends(authenticate_user)):
    # Query the chat session to update
    response = supabase.from_("chat_sessions_v2")\
        .update({"deleted_at": datetime.utcnow().isoformat()})\
        .eq("session_id", session_id)\
        .eq("user_id", user_id)\
        .execute()
    if not response.data:
        return []

    return response.data[0]


class UpdateChatSessionNameData(BaseModel):
    session_id: str
    session_name: str


# Update session name
@router.put("/chat-session-name")
async def update_chat_session_name(data: UpdateChatSessionNameData, user_id: str = Depends(authenticate_user)):
    # Query the chat session to update
    response = supabase.from_("chat_sessions_v2")\
        .update({"session_name": data.session_name, "updated_at": datetime.utcnow().isoformat()})\
        .eq("session_id", data.session_id)\
        .eq("user_id", user_id)\
        .execute()
    if not response.data:
        return []

    return response.data[0]


class UpdateChatSessionConversationData(BaseModel):
    session_id: str
    conversation: List[Any]


# Update a chat session in the database
@router.put("/chat-session-conversation")
async def update_chat_session_conversation(data: UpdateChatSessionConversationData, user_id: str = Depends(authenticate_user)):
    # Query the chat session to update
    response = supabase.from_("chat_sessions_v2")\
        .update({"conversation": data.conversation, "updated_at": datetime.utcnow().isoformat()})\
        .eq("session_id", data.session_id)\
        .eq("user_id", user_id)\
        .execute()
    if not response.data:
        return []

    return response.data[0]


# Get chat session sharing information by session ID
@router.get("/chat-session-sharing")
async def get_chat_session_sharing(session_sharing_id: str = Query(...)):
    print(session_sharing_id)
    # Query the chat session sharing information
    chat_session_sharing_response = supabase.from_("chat_sessions_sharing")\
        .select("*")\
        .eq("session_sharing_id", session_sharing_id)\
        .execute()
    if not chat_session_sharing_response.data:
        return []

    return chat_session_sharing_response.data[0]


# Define the request body model for adding a new chat session sharing
class AddChatSessionSharingData(BaseModel):
    session_id: str
    session_name: str
    course_title: str
    module_title: str
    chatbot_id: str
    chatbot_name: str
    conversation: List[Any]


# Add a new chat session sharing to the database
@router.post("/chat-session-sharing")
async def add_chat_session_sharing(data: AddChatSessionSharingData):
    # Insert a new chat session sharing into the database
    response = supabase.from_("chat_sessions_sharing").insert([{
            "session_id": data.session_id,
            "session_name": data.session_name,
            "course_title": data.course_title,
            "module_title": data.module_title,
            "chatbot_id": data.chatbot_id,
            "chatbot_name": data.chatbot_name,
            "conversation": data.conversation,
        }]).execute()
    if not response.data:
        return []

    return response.data[0]


# Upload files to the chat session
@router.post("/chat-session-attach-file")
async def attach_file(session_id: str = Form(...), chatbot_id: str = Form(...), file: UploadFile = File(...), user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    uploads_dir = "uploads"
    # Check if the uploads directory exists, if not create it
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)

    # Generate a unique filename
    file_extension = file.filename.split('.')[-1]
    unique_filename = f"{uuid4()}.{file_extension}"

    file_path = os.path.join(uploads_dir, unique_filename)
    async with aiofiles.open(file_path, 'wb') as out_file:
        content = await file.read()
        await out_file.write(content)

    # Point the file cursor to the beginning of the file
    await file.seek(0)

    file_content, res, doc_time_elapsed = analyze_document(file_path)

    # print(file_content)

    # Insert the file information into the database
    response = supabase.from_("doc_responses").insert([{
        "session_id": session_id,
        "chatbot_id": chatbot_id,
        "api_provider": "Azure Document Intelligence",
        "api_version": res.api_version,
        "model_id": res.model_id,
        "filename": unique_filename,
        "file_extension": file_extension,
        "file_path": file_path,
        "file_size": len(content),
        "original_filename": file.filename,
        "file_content": file_content,
        "doc_time_elapsed": doc_time_elapsed,
    }]).execute()

    if not response.data:
        raise HTTPException(status_code=404, detail="File upload failed")

    return {"filename": unique_filename, "original_filename": file.filename, "file_content": file_content}


# Upload an image file to OCR
@router.post("/chat-session-ocr-image")
async def upload_image_and_ocr(session_id: str = Form(...), chatbot_id: str = Form(...), image: UploadFile = File(...), user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    uploads_dir = "uploads"
    # Check if the uploads directory exists, if not create it
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)

    # Generate a unique filename
    file_extension = image.filename.split('.')[-1]
    unique_filename = f"{uuid4()}.{file_extension}"

    file_path = os.path.join(uploads_dir, unique_filename)
    async with aiofiles.open(file_path, 'wb') as out_file:
        content = await image.read()
        await out_file.write(content)

    # Point the file cursor to the beginning of the file
    await image.seek(0)

    ocr_result, res, ocr_time_elapsed = analyze_document(file_path)

    # print(file_content)

    # Insert the file information into the database
    response = supabase.from_("ocr_responses").insert([{
        "session_id": session_id,
        "chatbot_id": chatbot_id,
        "api_provider": "Azure Document Intelligence",
        "api_version": res.api_version,
        "model_id": res.model_id,
        "filename": unique_filename,
        "file_extension": file_extension,
        "file_path": file_path,
        "file_size": len(content),
        "original_filename": image.filename,
        "ocr_result": ocr_result,
        "ocr_time_elapsed": ocr_time_elapsed,
    }]).execute()

    if not response.data:
        raise HTTPException(status_code=404, detail="File upload failed")

    return {"filename": unique_filename, "original_filename": image.filename, "ocr_result": ocr_result}


# Handle Web Search
@router.post("/chat-session-web-search")
async def get_web_search_result(query: str = Form(...), user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    web_search_result, res, time_elapsed = await chat_by_openrouter_api(
        conversation_list=[{
            "role": "user",
            "content": query + "\n\n" + get_prompt_web_search()
        }],
        model_name="perplexity/llama-3.1-sonar-small-128k-online",
        temperature=0.5
    )

    return web_search_result


# Define the request body model for getting the updated checklist progress
class GetChecklistProgressData(BaseModel):
    session_id: str
    conversation_list: List[Any]
    checklist_items: str


# Handle Get Checklist Progress
@router.post("/chat-session-checklist-progress")
async def get_checklist_progress(data: GetChecklistProgressData, user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Combine the conversation list with the checklist items
    combined_conversation_list = data.conversation_list + [{
        "role": "user",
        "content": get_prompt_checklist_progress() + "\n\n```\n" + data.checklist_items + "\n```"
    }]

    response_format = {
        "type": "json_schema",
        "json_schema": {
            "name": "checklist_progress",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "checklist_progress": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "item_id": {
                                    "type": "string"
                                },
                                "item_content": {
                                    "type": "string"
                                },
                                "completed": {
                                    "type": "boolean"
                                }
                            },
                            "required": ["item_id", "item_content", "completed"],
                            "additionalProperties": False
                        }
                    }
                },
                "required": ["checklist_progress"],
                "additionalProperties": False
            }
        }
    }

    checklist_progress, res, time_elapsed = await chat_by_openrouter_api(
        conversation_list=combined_conversation_list,
        model_name="openai/gpt-4.1-mini",
        temperature=0.5,
        response_format=response_format
    )
    
    if not checklist_progress:
        raise HTTPException(status_code=404, detail="No checklist progress found")
    
    # Get current description
    current_description_response = supabase.from_("chat_sessions_v2")\
        .select("description")\
        .eq("session_id", data.session_id)\
        .execute()
    
    if not current_description_response.data:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    import json
    # Update checklist_progress in description
    current_description = current_description_response.data[0]["description"] or {}
    loaded_checklist_progress = json.loads(checklist_progress)
    current_description["checklist_progress"] = loaded_checklist_progress['checklist_progress']
    
    # Update description in database
    response = supabase.from_("chat_sessions_v2")\
        .update({"description": current_description})\
        .eq("session_id", data.session_id)\
        .execute()

    return current_description['checklist_progress']


# Define the request body model for getting the updated checklist progress
class GetChatSessionSummaryData(BaseModel):
    session_id: str
    conversation_list: List[Any]
    session_summary_prompt: str


# Handle Get Session Summary
@router.post("/chat-session-summary")
async def get_session_summary(data: GetChatSessionSummaryData, user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    # Combine the conversation list with the session summary prompt
    combined_conversation_list = data.conversation_list + [{
        "role": "user",
        "content": data.session_summary_prompt
    }]

    session_summary, res, time_elapsed = await chat_by_openrouter_api(
        conversation_list=combined_conversation_list,
        model_name="openai/gpt-4.1",
        temperature=0.5
    )

    # Get current description
    current_description_response = supabase.from_("chat_sessions_v2")\
        .select("description")\
        .eq("session_id", data.session_id)\
        .execute()
    
    if not current_description_response.data:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Update session_summary in description
    current_description = current_description_response.data[0]["description"] or {}
    if "qualitative_report" not in current_description:
        current_description["qualitative_report"] = {}
    current_description["qualitative_report"]["session_summary"] = session_summary
    
    # Update description in database
    response = supabase.from_("chat_sessions_v2")\
        .update({"description": current_description})\
        .eq("session_id", data.session_id)\
        .execute()
    
    return session_summary


# Add a new endpoint to get chat session description by session ID
@router.get("/chat-session-description")
async def get_chat_session_description(session_id: str, user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    # Query the chat session information
    response = supabase.from_("chat_sessions_v2")\
        .select("description")\
        .eq("session_id", session_id)\
        .execute()
    if not response.data:
        raise HTTPException(status_code=404, detail="Chat session not found")

    return response.data[0]


# Define the request body model for updating the chat session description
class UpdateChatSessionDescriptionData(BaseModel):
    session_id: str
    description: dict  # Change to accept a dictionary


# Handle Chat Session Description Update
@router.put("/chat-session-description")
async def update_chat_session_description(data: UpdateChatSessionDescriptionData, user_id: str = Depends(authenticate_user)):
    # Query user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    # Get current description
    current_description_response = supabase.from_("chat_sessions_v2")\
        .select("description")\
        .eq("session_id", data.session_id)\
        .execute()
    
    if not current_description_response.data:
        raise HTTPException(status_code=404, detail="Chat session not found")
    
    # Merge new description with existing one
    current_description = current_description_response.data[0]["description"] or {}
    merged_description = {
        "checklist_progress": data.description.get("checklist_progress", current_description.get("checklist_progress", [])),
        "quantitative_report": {
            "turn_count": data.description.get("quantitative_report", {}).get("turn_count", current_description.get("quantitative_report", {}).get("turn_count", 0)),
            "user_word_count": data.description.get("quantitative_report", {}).get("user_word_count", current_description.get("quantitative_report", {}).get("user_word_count", 0)),
            "chatbot_word_count": data.description.get("quantitative_report", {}).get("chatbot_word_count", current_description.get("quantitative_report", {}).get("chatbot_word_count", 0)),
            "conversation_time": data.description.get("quantitative_report", {}).get("conversation_time", current_description.get("quantitative_report", {}).get("conversation_time", 0))
        },
        "qualitative_report": {
            "session_summary": data.description.get("qualitative_report", {}).get("session_summary", current_description.get("qualitative_report", {}).get("session_summary", ""))
        },
        "user_feedback": data.description.get("user_feedback", current_description.get("user_feedback", {}))
    }
    
    # Update the merged description
    response = supabase.from_("chat_sessions_v2")\
        .update({"description": merged_description, "updated_at": datetime.utcnow().isoformat()})\
        .eq("session_id", data.session_id)\
        .eq("user_id", user_id)\
        .execute()
    if not response.data:
        return []

    return response.data[0]

