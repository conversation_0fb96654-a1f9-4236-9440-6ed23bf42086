from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routers import (
    auth,
    users,
    courses,
    modules,
    chatbots,
    chat_sessions,
    messages,
    notifications,
    internal_messages,
    groups,
    enrollments,
    video,
    avatars,
)

app = FastAPI()

# Permitted CORS Sources
origins = [
    "http://localhost:9000",  # Add your front-end development address
    "http://127.0.0.1:9000",  # If you have another front-end address, you can also add it here
    "http://127.0.0.1:4000",
    "https://bytewisehk.netlify.app",
    "https://bytewise-v3.netlify.app",
    "https://bytewise-v3-test.netlify.app",
    "https://bytewise-v3-release.netlify.app",
    "https://new.bytewise.hk",
    "https://chat.hkbu.life",
    "http://*************",
]

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,  # Permitted sources
    allow_credentials=True,  # Permitted credentials
    allow_methods=["*"],  # Permitted HTTP methods
    allow_headers=["*"],  # Permitted HTTP headers
)

# Include routers
app.include_router(auth.router)
app.include_router(users.router)
app.include_router(courses.router)
app.include_router(modules.router)
app.include_router(chatbots.router)
app.include_router(chat_sessions.router)
app.include_router(messages.router)
app.include_router(notifications.router)
app.include_router(internal_messages.router)
app.include_router(groups.router)
app.include_router(enrollments.router)
app.include_router(video.router)
app.include_router(avatars.router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
